from typing import List
import logging

import pandas as pd
import json

from cerebro.advisor.campaign_framework.abstract_challenge import AbstractChallenge
from cerebro.advisor.db.models.checkpoint import Checkpoint
from cerebro.advisor.storage.kelda_repo import <PERSON><PERSON>Repo
from cerebro.advisor.campaign_framework.calculation_helpers import CalculationHelpers
from cerebro.advisor.config.config import Config
from cerebro.advisor.utils.insight_utils import InsightUtils
from cerebro.advisor.db.models.challenge_results.result_paid_vs_organic import TenzingResultPaidVsOrganic


class ReducePaidCoverage(AbstractChallenge):
    def get_challenge_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        return KeldaRepo.get_relevant_query_info_by_stg(self.account_id, self.region, self.search_term_group, start_date, end_date)

    def check_progress(self, check_point_df: pd.DataFrame):
        pass

    def prepare_data(self):
        self.data = self.data if self.data else self.get_challenge_data(self.get_start_date(), self.get_end_date())
        self.data = self.data.sort_values(by=['date'], ascending=True)
        logging.log(level=logging.INFO, msg="loaded data for ReducePaidCoverage")

    def calculate_opportunity(self):
        mobile_data = CalculationHelpers.get_device_data(self.data, 'mobile')
        desktop_data = CalculationHelpers.get_device_data(self.data, 'desktop')

        # get all the terms we see the customer advertising on
        client_mobile_paid_search_terms = CalculationHelpers.get_paid_domain_data(mobile_data, self.domain)
        client_desktop_paid_search_terms = CalculationHelpers.get_paid_domain_data(desktop_data, self.domain)

        # get all client organic terms where client is not appearing on paid
        high_rank_mobile_customer_organic_only_df = mobile_data[(mobile_data['domain'] == self.domain) & (mobile_data['rank'] < 4) & (~mobile_data['search_term'].isin(client_mobile_paid_search_terms['search_term']))]
        high_rank_desktop_customer_organic_only_df = desktop_data[(desktop_data['domain'] == self.domain) & (desktop_data['rank'] < 4) & (~desktop_data['search_term'].isin(client_desktop_paid_search_terms['search_term']))]

        mobile_opportunity_terms = mobile_data.loc[((mobile_data['ad_type'] == 'textad') | (mobile_data['ad_type'] == 'pla')) & (mobile_data['search_term'].isin(high_rank_mobile_customer_organic_only_df['search_term']))]
        desktop_opportunity_terms = desktop_data.loc[((desktop_data['ad_type'] == 'textad') | (desktop_data['ad_type'] == 'pla')) & (desktop_data['search_term'].isin(high_rank_desktop_customer_organic_only_df['search_term']))]

        mobile_opportunity_terms = mobile_opportunity_terms.drop(columns=['account_id', 'date', 'impressions', 'clicks', 'spend', 'device', 'ad_type', 'domain', 'rank'])
        desktop_opportunity_terms = desktop_opportunity_terms.drop(columns=['account_id', 'date', 'impressions', 'clicks', 'spend', 'device', 'ad_type', 'domain', 'rank'])

        mobile_opportunity_terms = mobile_opportunity_terms.groupby(by=['search_term']).max().reset_index().sort_values(by=['clicks_unbounded'], ascending=False).round(2)
        desktop_opportunity_terms = desktop_opportunity_terms.groupby(by=['search_term']).max().reset_index().sort_values(by=['clicks_unbounded'], ascending=False).round(2)

        self.mobile_opportunity_terms = mobile_opportunity_terms[:Config.OPPORTUNITY_TERMS]
        self.desktop_opportunity_terms = desktop_opportunity_terms[:Config.OPPORTUNITY_TERMS]

    def set_result_type(self):
        self.result_type = TenzingResultPaidVsOrganic

    def calculate_scores(self):
        if self.mobile_opportunity_terms.size > 0 or self.desktop_opportunity_terms.size > 0:
            score = int(self.mobile_opportunity_terms['clicks_unbounded'].sum()) + int(self.desktop_opportunity_terms['clicks_unbounded'].sum())
            data = {
                'score': [score],
                'href': [self.data_visualisation_url()],
                'message': [f"{score} points available to reduce your paid coverage where you are strong in organic."]
            }

            if self.desktop_opportunity_terms.size > 0:
                data['kr1'] = f"Your top desktop opportunity to reduce paid coverage is for {InsightUtils.number_format(int(self.desktop_opportunity_terms['clicks_unbounded'].values[0]))} clicks on the search term '{self.desktop_opportunity_terms['search_term'].values[0]}'"
            else:
                data['kr1'] = f"You have no desktop opportunity to reduce paid coverage"

            if self.mobile_opportunity_terms.size > 0:
                data['kr2'] = f"Your top mobile opportunity to reduce paid coverage is for {InsightUtils.number_format(int(self.mobile_opportunity_terms['clicks_unbounded'].values[0]))} clicks on the search term '{self.mobile_opportunity_terms['search_term'].values[0]}'"
            else:
                data['kr2'] = f"You have no mobile opportunity to reduce paid coverage"

            data['kr3'] = f"What should we put here?"
        else:
            score = 0
            data = {
                'score': [score],
                'href': [self.data_visualisation_url()],
                'message': [f"We could not find and opportunities to take advantage of your organic strength in this group."]
            }

            data['kr1'] = f"No desktop opportunities found to reduce paid coverage"
            data['kr2'] = f"No mobile opportunities found to reduce paid coverage"
            data['kr3'] = f"What should we put here?"

        self.score_results = pd.DataFrame(data)

    def data_visualisation_url(self) -> str:
        return f"/strategy/reduce_paid_coverage/{self.account_id}/{self.campaign_id}/{self.challenge_id}/{self.region}/{self.insight_date}"

