from flask import render_template, flash, redirect, session, url_for, request
from cerebro.web.main.main_controller import main_bp
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler

import locale
locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')

from cerebro.utils.funcs import *
from cerebro.sql_queries import industry_data_queries
from cerebro.sql_queries import prospect_queries
from cerebro.sii_scores import sii_redis


NUM_TERMS = 25


@main_bp.route('/all_scores')
def all_scores():
    engine_id = request.args.get('engine')
    category1 = request.args.get('category1')
    category2 = request.args.get('category2')
    category3 = request.args.get('category3')
    
    device = "Desktop"

    if(category3):
        redis_key = sii_redis.build_key(category3, engine_id, device)
    else:
        redis_key = sii_redis.build_key(category2, engine_id, device)

    df_vertical = sii_redis.get_dataframe_from_redis(redis_key)
    tableData = df_vertical.to_json(orient='records')

    """df = prospect_queries.getCatTwoProspectDimensionData(engine, category2, NUM_TERMS)
    df.drop(['total_volume'], inplace=True, axis=1)
    df.drop(['total_spend'], inplace=True, axis=1)
    df.drop(['total_clicks'], inplace=True, axis=1)
    df.drop(['position_score'], inplace=True, axis=1)
    df.drop(['engine_id'], inplace=True, axis=1)
    df.drop(['search_terms'], inplace=True, axis=1)
    tableData = df.to_json(orient='records')"""

    return render_template(
        'industry_benchmarking/all_scores.html',
        engine=engine_id,
        td=tableData,
        category1=category1,
        category2=category2
    )

#['domain', 'BRAND OWNERSHIP', 'CLASS LEADERSHIP', 'CMO Score', 'MARKET PERFORMANCE', 'avg. position', 'brand bidding', 'brand position 1', 'brand term infringment', 'brand terms coverage', 'click share', 'cpc', 'ctr', 'impression share', 'search term coverage', 'spend share']
@main_bp.route('/industry_benchmark')
def industry_benchmark():
    engine_id = request.args.get('engine')
    category1 = request.args.get('category1')
    category2 = request.args.get('category2')
    category3 = request.args.get('category3')

    device = "Desktop"

    if(category3):
        redis_key = sii_redis.build_key(category3, engine_id, device)
    else:
        redis_key = sii_redis.build_key(category2, engine_id, device)

    print(redis_key)

    df_vertical = sii_redis.get_dataframe_from_redis(redis_key)
    dfp = df_vertical.pivot(index='domain', columns='Metric', values='score(out of 100)')
    dfp.reset_index(level=0, inplace=True)
    tableData = dfp.to_json(orient='records')
    headers = list(dfp.columns.values)
    titles = []

    for header in headers:
        title = {'title': header}
        titles.append(title)

    return render_template(
        'industry_benchmarking/industry_benchmark.html',
        engine=engine_id,
        td=tableData,
        titles=titles,
        category1=category1,
        category2=category2
    )
