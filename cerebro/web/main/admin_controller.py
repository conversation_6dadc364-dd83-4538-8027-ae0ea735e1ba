import json
import logging
import traceback
import time

import pandas as pd
from datetime import date, timedelta
from functools import reduce
from flask import render_template, request, jsonify, Response
from flask_login import login_required, current_user
from flask import current_app

logger = logging.getLogger(__name__)

from cerebro.services import db_queries
from cerebro.services import redis_repo
from cerebro.services.basic_auth import basic_auth
from cerebro.utils.funcs import *
from cerebro.web.main.main_controller import main_bp
import cerebro.health_dashboard.queries as health_queries
import os

@main_bp.route('/account_cleaner')
@login_required
def account_cleaner():
    data = db_queries.get_all_account_infos()

    return render_template(
        'admin/account_info.html',
        data=json.dumps(data.values.tolist())
    )


def get_cache_item(key):
    flask_cache = current_app.extensions.get('flask_cache')
    cached_item = flask_cache.get(key)
    return cached_item


def set_cache_item(key, item, ttl=60*300):
    flask_cache = current_app.extensions.get('flask_cache')
    flask_cache.set(key, item, timeout=ttl)


@main_bp.route('/api-trend-data')
def api_trend_data():
    data = get_cache_item('api_trend_data')

    if data is None:
        data = db_queries.get_api_trend_data()
        set_cache_item('api_trend_data', data)

    json_data = data.to_json(orient='records')
    return Response(response=json_data, status=200, mimetype="application/json")


@main_bp.route('/api-trend-data-looker')
def looker_api_trend_data():
    data = get_cache_item('looker_api_trend_data')

    if data is None:
        data = db_queries.get_api_trend_data_looker()
        set_cache_item('looker_api_trend_data', data)

    json_data = data.to_json(orient='records')
    return Response(response=json_data, status=200, mimetype="application/json")


@main_bp.route('/kelda-eu-stats')
def kelda_eu_stats():
    data = get_cache_item('get_kelda_eu_stats')
    daily_stats = get_cache_item('kelda_eu_daily_stats')

    if data is None:
        data = db_queries.get_kelda_eu_stats()
        daily_stats = db_queries.get_kelda_eu_stats_daily()
        set_cache_item('get_kelda_eu_stats', data, 60*5)
        set_cache_item('kelda_eu_daily_stats', daily_stats, 60*5)

    data = data.sort_values(by='query_date')
    daily_stats = daily_stats.sort_values(by='start_time')

    return render_template(
        'admin/kelda_eu_stats.html',
        data=json.dumps(data.values.tolist()),
        daily_stats=json.dumps(daily_stats.values.tolist())
    )


@main_bp.route('/sigma-stats')
def sigma_stats():
    data = get_cache_item('get_sigma_stats')
    daily_stats = get_cache_item('sigma_daily_stats')

    if data is None:
        data = db_queries.get_sigma_stats()
        daily_stats = db_queries.get_sigma_stats_daily()
        set_cache_item('get_sigma_stats', data, 60*3)
        set_cache_item('sigma_daily_stats', daily_stats, 60*3)

    data = data.sort_values(by='query_date')
    daily_stats = daily_stats.sort_values(by='start_time')

    return render_template(
        'admin/sigma_stats.html',
        data=json.dumps(data.values.tolist()),
        daily_stats=json.dumps(daily_stats.values.tolist())
    )


@main_bp.route('/cube-stats')
def cube_stats():
    data = get_cache_item('get_cube_stats')
    daily_stats = get_cache_item('cube_daily_stats')

    if data is None:
        data = db_queries.get_cube_stats()
        daily_stats = db_queries.get_cube_stats_daily()
        set_cache_item('get_cube_stats', data, 60*3)
        set_cache_item('cube_daily_stats', daily_stats, 60*3)

    data = data.sort_values(by='query_date')
    daily_stats = daily_stats.sort_values(by='start_time')

    return render_template(
        'admin/cube_stats.html',
        data=json.dumps(data.values.tolist()),
        daily_stats=json.dumps(daily_stats.values.tolist())
    )


@main_bp.route('/api_requests')
@login_required
def api_requests():
    data = get_cache_item('get_api_domain_data')
    if data is None:
        data = db_queries.get_api_domain_data()
        set_cache_item('get_api_domain_data', data)

    resource_usage = get_cache_item('get_api_resource_usage')
    if resource_usage is None:
        resource_usage = db_queries.get_api_resource_usage()
        set_cache_item('get_api_resource_usage', resource_usage)

    resource_usage_by_domain = get_cache_item('get_api_resource_usage_by_domain')
    if resource_usage_by_domain is None:
        resource_usage_by_domain = db_queries.get_api_resource_usage_by_domain()
        set_cache_item('get_api_resource_usage_by_domain', resource_usage_by_domain)

    looker_data = get_cache_item('get_api_domain_data_looker')
    if looker_data is None:
        looker_data = db_queries.get_api_domain_data_looker()
        set_cache_item('get_api_domain_data_looker', looker_data)

    looker_resource_usage = get_cache_item('get_api_resource_usage_looker')
    if looker_resource_usage is None:
        looker_resource_usage = db_queries.get_api_resource_usage_looker()
        set_cache_item('get_api_resource_usage_looker', looker_resource_usage)

    looker_resource_usage_by_domain = get_cache_item('get_api_resource_usage_by_domain_looker')
    if looker_resource_usage_by_domain is None:
        looker_resource_usage_by_domain = db_queries.get_api_resource_usage_by_domain_looker()
        set_cache_item('get_api_resource_usage_by_domain_looker', looker_resource_usage_by_domain)

    return render_template(
        'admin/api_requests.html',
        domain_data=json.dumps(data.values.tolist()),
        resource_usage_data=json.dumps(resource_usage.values.tolist()),
        resource_usage_by_domain=json.dumps(resource_usage_by_domain.values.tolist()),
        looker_domain_data=json.dumps(looker_data.values.tolist()),
        looker_resource_usage_data=json.dumps(looker_resource_usage.values.tolist()),
        looker_resource_usage_by_domain=json.dumps(looker_resource_usage_by_domain.values.tolist())
    )


@main_bp.route('/account_infos')
@login_required
def account_infos():
    data = db_queries.get_all_account_infos()

    # Get unique industries, filter out None values, and sort them alphabetically
    industries = sorted([industry for industry in data['industry'].unique() if industry is not None])

    # Get unique industry verticals, filter out None values, and sort them alphabetically
    industry_verticals = sorted([industry_vertical for industry_vertical in data['industry_vertical'].unique() if industry_vertical is not None])

    return render_template(
        'admin/account_info.html',
        data=json.dumps(data.values.tolist()),
        industries=industries,
        industry_verticals=industry_verticals
    )

@main_bp.route('/ba_savings_report')
@login_required
def ba_savings_report():
    today = datetime.today()
    start_date = request.args.get('start_date', default=today.replace(day=1).strftime('%Y-%m-%d'), type=str)
    end_date = request.args.get('end_date', default=today.strftime('%Y-%m-%d'), type=str)
    is_standalone = request.args.get('is_standalone', default='false', type=str)
    data = db_queries.get_ba_savings_report(is_standalone.upper(), start_date, end_date)

    return render_template(
        'admin/ba_savings_report.html',
        data=json.dumps(data.values.tolist()),
        is_standalone=is_standalone.lower(),
        start_date=start_date,
        end_date=end_date
    )


@main_bp.route('/arlo_logs')
@login_required
def arlo_logs():
    data = db_queries.get_chat_bot_log()
    client_arlo_data = db_queries.get_arlo_log_client()
    adthena_arlo_data = db_queries.get_arlo_log_adthena()
    adthena_arlo_data_thumbs_down = db_queries.get_arlo_log_adthena_thumbs_down()
    adthena_arlo_client_usage_ranks = db_queries.get_arlo_log_client_usage_ranks()
    adthena_arlo_staff_ranks = db_queries.get_adthena_arlo_staff_ranks()
    adthena_arlo_client_user_ranks = db_queries.get_adthena_arlo_client_user_ranks()

    return render_template(
        'admin/chat_bot_log.html',
        data=json.dumps(data.values.tolist()),
        client_arlo_data=json.dumps(client_arlo_data.values.tolist()),
        adthena_arlo_data=json.dumps(adthena_arlo_data.values.tolist()),
        adthena_arlo_data_thumbs_down=json.dumps(adthena_arlo_data_thumbs_down.values.tolist()),
        adthena_arlo_client_usage_ranks=json.dumps(adthena_arlo_client_usage_ranks.values.tolist()),
        adthena_arlo_staff_ranks=json.dumps(adthena_arlo_staff_ranks.values.tolist()),
        adthena_arlo_client_user_ranks=json.dumps(adthena_arlo_client_user_ranks.values.tolist())
    )


@main_bp.route('/account_term_counts')
@login_required
def account_term_counts():
    data = db_queries.get_account_term_counts()

    return render_template(
        'admin/account_term_counts.html',
        data=json.dumps(data.values.tolist())
    )


def _account_health_dashboard() -> pd.DataFrame:
    data = pd.DataFrame(health_queries.get_account_health_data())
    cap_df = health_queries.get_search_term_hourly_term_cap_infos()
    data['unhealthy'] = data['forced_terms'] >= data['package_size']

    data = data.merge(cap_df[['account_id', 'hourly_terms_limit']], left_on='account_id', right_on='account_id')
    # ordering
    return data[['account_id', 'domain', 'country', 'forced_terms', 'hourly_terms',
                 'relevant_hourly_terms', 'hourly_terms_limit',
                 'my_search_terms', 'terms_in_user_groups', 'package_size',
                 'available_size', 'package_volume', 'total_volume', 'unhealthy']]


@main_bp.route('/account_health_dashboard')
@login_required
def account_health_dashboard():
    return render_template(
        'admin/account_health_dashboard.html',
        data=json.dumps(_account_health_dashboard().values.tolist())
    )

@main_bp.route('/account_health_dashboard_json', methods=['GET'])
@basic_auth.login_required
def account_health_dashboard_json():
    return jsonify(_account_health_dashboard().to_dict(orient='records'))

@main_bp.route('/missing_pla_domains')
@login_required
def missing_pla_domains():
    data = db_queries.get_missing_pla_domains()

    permission = True if current_user.id in ADMINS else False

    return render_template(
        'admin/missing_pla_domains.html',
        permission=permission,
        data=json.dumps(data.values.tolist())
    )


@main_bp.route('/update_missing_pla_domains')
@login_required
def update_missing_pla_domains():
    display_text = request.args.get('display_text').lower().strip()
    engine_id = int(request.args.get('engine_id'))
    domain = request.args.get('domain')

    text = """{} updated missing pla text {} with domain {} for engine {}""".format(current_user.id, display_text, domain, engine_id)
    print(text)

    with open('./missing_pla_updates.txt', 'a+') as the_file:
        the_file.write(text + '\n')

    db_queries.update_missing_pla_domain(display_text, engine_id, domain)

    response = app.response_class(
        response={'res': 'BOOM'},
        status=200,
        mimetype='application/json'
    )
    return response


@main_bp.route('/health_check')
@login_required
def health_check():
    data = db_queries.get_all_users_for_health_check()

    permission = True if current_user.id in ADMINS else False

    return render_template(
        'admin/health_check.html',
        permission=permission,
        data=json.dumps(data.values.tolist())
    )


@main_bp.route('/ideas')
@login_required
def ideas():
    return render_template('admin/ideas.html')


@main_bp.route('/legacy_account_insights')
@login_required
def legacy_account_insights():
    return render_template('admin/legacy_account_insights.html')


@main_bp.route('/legacy_categorisation')
@login_required
def legacy_categorisation():
    return render_template('admin/legacy_categorisation.html')


@main_bp.route('/flush_redis')
@login_required
def flush_redis():
    redis_repo.flush_all()
    return render_template('admin/flush_redis.html')

@main_bp.route('/bigquery_data_transfers')
@login_required
def bigquery_data_transfers():
    data = db_queries.get_bigquery_data_transfers()

    return render_template(
        'admin/bigquery_data_transfers.html',
        data=json.dumps(data.to_dict('records'))
    )

@main_bp.route('/mcc_bigquery_data_transfers')
@login_required
def mcc_bigquery_data_transfers():
    latest_transfer_data = db_queries.get_latest_transfer_datetime()
    missing_mcc_transfers = db_queries.get_mcc_data()
    customers_without_gcid = db_queries.get_list_of_customers_without_gcid(ttl_hash=db_queries.get_ttl_hash())

    return render_template(
        'admin/mcc_bigquery_data_transfers.html',
        latest_transfer_data=latest_transfer_data.to_dict(orient='records'),
        missing_mcc_transfers=missing_mcc_transfers.to_dict(orient='records'),
        customers_without_gcid=customers_without_gcid.to_dict(orient='records')
    )

@main_bp.route("/affiliate_network_matches")
@login_required
def affiliate_network_matches():
    PERIOD_LENGTH = 30
    LAG_DAYS = 2

    current_date = date.today()
    period_delta = timedelta(days=PERIOD_LENGTH - 1)

    current_period_end = current_date - timedelta(days=LAG_DAYS)
    current_period_start = current_period_end - period_delta
    previous_period_end = current_period_start - timedelta(days=1)
    previous_period_start = previous_period_end - period_delta

    affiliates = db_queries.get_affiliate_networks()
    data_1st_period = db_queries.get_affiliate_network_matches(current_period_start, current_period_end).rename(columns={"matches": "matches_1st_period"})
    data_2nd_period = db_queries.get_affiliate_network_matches(previous_period_start, previous_period_end).rename(columns={"matches": "matches_2nd_period"})

    df = reduce(lambda left, right: pd.merge(left, right, on="affiliate_network_id", how="left"), [affiliates, data_1st_period, data_2nd_period])
    df.fillna({"matches_1st_period": 0}, inplace=True)
    df["change_percentage"] = (df["matches_2nd_period"] - df["matches_1st_period"]) / df["matches_2nd_period"]

    return render_template(
        "admin/affiliate_network_matches.html",
        data=json.dumps(df.to_dict("records"))
    )


@main_bp.route("/alpha_report_editor")
@login_required
def alpha_report_editor():
    from cerebro.bender.adthena_data.db_connector import get_jerry_engine
    from flask import Flask, render_template, request, redirect, url_for
    import uuid

    if request.method == 'POST':
        # Generate GUID
        template_id = str(uuid.uuid4())

        # Get data from form
        user_email = request.form.get('user_email')
        template_name = request.form.get('template_name')
        account_id = request.form.get('account_id')
        account_domain = request.form.get('account_domain')
        wmv = request.form.get('wmv')

        # Steps (multiple entries)
        step_names = request.form.getlist('step_name')
        step_order_numbers = request.form.getlist('step_order_number')
        queries = request.form.getlist('query')

        # Schedule information
        recipients = request.form.get('recipients')
        time = request.form.get('time')
        timezone = request.form.get('timezone')
        day_type = request.form.get('day_type')
        days_of_week = request.form.get('days_of_week')
        day_of_month = request.form.get('day_of_month')
        frequency = request.form.get('frequency')
        token = request.form.get('token')
        client_email = request.form.get('client_email')

        # Database insertion

        j_engine = get_jerry_engine()
        conn = j_engine.connect()
        cur = conn.cursor()

        # Insert into arlo_templates
        cur.execute("""
                INSERT INTO public.arlo_templates (user_email, template_id, template_name, account_id)
                VALUES (%s, %s, %s, %s)
            """, (user_email, template_id, template_name, account_id))

        # Insert into arlo_template_steps
        for step_name, step_order_number, query in zip(step_names, step_order_numbers, queries):
            cur.execute("""
                    INSERT INTO public.arlo_template_steps
                    (template_id, user_email, step_name, step_order_number, query, account_id, account_domain, stg_name, competitor_group_name, wmv)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                template_id, user_email, step_name, step_order_number, query,
                account_id, account_domain, 0, 0, wmv
            ))

        # Insert into arlo_schedules
        cur.execute("""
                INSERT INTO arlo_schedules (template_id, day_type, days_of_week, day_of_month, time, timezone, frequency, recipients, token, client_email)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
            template_id, day_type, days_of_week, day_of_month, time,
            timezone, frequency, recipients, token, client_email
        ))

        conn.commit()
        cur.close()
        conn.close()
    return render_template('admin/alpha_arlo_report_editor.html')

@main_bp.route('/ba_bp_usage')
@login_required
def ba_bp_usage():
    ahj_data = db_queries.get_ahj_clients()
    atd_data = db_queries.get_atd_clients()
    ba_data = db_queries.get_ba_clients()

    return render_template(
        'admin/ba_bp_usage.html',
        ahj_data=json.dumps(ahj_data.values.tolist()),
        atd_data=json.dumps(atd_data.values.tolist()),
        ba_data=json.dumps(ba_data.values.tolist())
    )


@main_bp.route('/policy_restricted_accounts')
@login_required
def policy_restricted_accounts():
    pr_data = db_queries.get_policy_restricted_accounts()

    return render_template(
        'admin/policy_restricted_accounts.html',
        pr_data=json.dumps(pr_data.values.tolist())
    )


@main_bp.route('/search_term_text_ad_ratios', methods=['GET'])
@login_required
def get_search_term_text_ad_ratios():
    try:
        account_id = request.args.get('account_id')
        region = request.args.get('region')
        sub_region = request.args.get('sub_region') if region == 'US' else None
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        if not account_id or not region:
            return jsonify({'error': 'Missing required parameters: account_id and region'}), 400

        # Validate and parse date formats
        try:
            if start_date:
                # Try to parse in YYYY-MM-DD format first
                try:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                except ValueError:
                    # If that fails, try MM/DD/YYYY format
                    start_date = datetime.strptime(start_date, '%m/%d/%Y').date()
            
            if end_date:
                # Try to parse in YYYY-MM-DD format first
                try:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                except ValueError:
                    # If that fails, try MM/DD/YYYY format
                    end_date = datetime.strptime(end_date, '%m/%d/%Y').date()
        except ValueError as e:
            logger.error(f"Date parsing error: {str(e)}")
            return jsonify({'error': f'Invalid date format: {str(e)}'}), 400

        result = db_queries.get_search_term_text_ad_ratios(account_id, region, sub_region, start_date, end_date)

        search_terms_data = []
        for _, row in result.iterrows():
            search_terms_data.append({
                'search_term': row['searchterm'],
                'search_volume': float(row['search_volume']),
                'text_ad_ratio': float(row['textad_ratio']),
                'total_scrape_count': int(row['total_scrape_count']),
                'total_appearances': int(row['total_appearances'])
            })

        return jsonify({
            'account_id': account_id,
            'region': region,
            'sub_region': sub_region,
            'start_date': start_date.strftime('%Y-%m-%d') if start_date else None,
            'end_date': end_date.strftime('%Y-%m-%d') if end_date else None,
            'search_terms': search_terms_data
        })
    except Exception as e:
        logger.error(f"Error in search_term_text_ad_ratios API: {str(e)}\n{traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500


@main_bp.route('/text_ad_ratios_by_region', methods=['GET'])
@login_required
def get_text_ad_ratios_by_region():
    try:
        region = request.args.get('region')
        sub_region = request.args.get('sub_region') if region == 'US' else None
        account_ids_param = request.args.get('account_ids', '')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        account_ids = [int(id.strip()) for id in account_ids_param.split(',') if id.strip()]

        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()

        ratios_df = db_queries.get_text_ad_ratios_by_region(region, sub_region, account_ids, start_date, end_date)

        ratios_dict = {}
        for _, row in ratios_df.iterrows():
            ratios_dict[str(int(row['accountid']))] = float(row['textad_ratio'])

        return jsonify({
            'region': region,
            'sub_region': sub_region,
            'text_ad_ratios': ratios_dict
        })
    except Exception as e:
        logger.error(f"Error in text_ad_ratios_by_region : {str(e)}\n{traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500


@main_bp.route('/looker-usage')
@login_required
def analyze_looker_usage():
    import os
    import pandas as pd
    from flask import current_app, render_template

    csv_path = os.path.join(current_app.root_path).replace('/cerebro/web', '/notebooks/looker_usage.csv')

    try:
        df = pd.read_csv(csv_path, low_memory=False)
    except Exception as e:
        return f"Error loading CSV: {str(e)}"

    if 'Owner' not in df.columns:
        return f"Error: 'Owner' column not found. Available columns: {df.columns.tolist()}"

    analysis_data = {}

    for owner in df['Owner'].unique():
        owner_df = df[df['Owner'] == owner].copy()
        external_views = owner_df[~owner_df['Actor'].str.contains('@adthena', na=False)].copy()
        if external_views.empty or 'Date' not in external_views.columns:
            continue

        # Convert the 'Date' column to datetime using an explicit format
        external_views['Date'] = pd.to_datetime(
            external_views['Date'],
            format='%Y-%m-%dT%H:%M:%SZ',
            errors='coerce'
        )

        # Check if conversion was successful
        print("Date column dtype:", external_views['Date'].dtype)

        # Drop rows with invalid dates
        external_views = external_views.dropna(subset=['Date'])

        # Format the datetime as a string in the desired format
        external_views['Date'] = external_views['Date'].dt.strftime('%Y-%m-%d')

        # Group by Asset, Date, and Actor to get daily views per person
        daily_views = (
            external_views.groupby(['Asset name', 'Date', 'Actor'])
            .size().reset_index(name='Views')
        )

        # Build summary data per asset
        summary_df = (
            daily_views.groupby('Asset name')['Views']
            .sum().reset_index().sort_values('Views', ascending=False)
        )
        actor_count_df = (
            external_views.groupby('Asset name')['Actor']
            .nunique().reset_index()
        )
        actor_count_df.columns = ['Asset name', 'Unique External Viewers']
        summary_df = summary_df.merge(actor_count_df, on='Asset name')
        summary_list = summary_df.to_dict(orient='records')
        for record in summary_list:
            record['Views'] = int(record['Views'])
            record['Unique External Viewers'] = int(record['Unique External Viewers'])

        charts = {}
        assets = daily_views['Asset name'].unique()
        for asset in assets:
            asset_data = daily_views[daily_views['Asset name'] == asset]

            # Overall daily views line chart data
            overall_daily = (
                asset_data.groupby('Date')['Views']
                .sum().reset_index().sort_values('Date')
            )
            overall_line_chart = {
                "dates": overall_daily['Date'].tolist(),
                "views": overall_daily['Views'].tolist()
            }

            # Build trend data by person:
            pivot = asset_data.pivot_table(index='Date', columns='Actor', values='Views', fill_value=0)
            if pivot.empty:
                continue
            sorted_dates = sorted(pivot.index)
            trend_datasets = []
            for actor in pivot.columns:
                actor_views = [int(pivot.loc[date, actor]) for date in sorted_dates]
                trend_datasets.append({
                    "label": actor,
                    "data": actor_views,
                    "borderColor": "rgba(75, 192, 192, 1)",
                    "fill": False
                })
            trend_chart = {
                "dates": sorted_dates,
                "datasets": trend_datasets
            }

            charts[asset] = {
                "line": overall_line_chart,
                "trend": trend_chart
            }

        analysis_data[owner] = {
            "summary": summary_list,
            "charts": charts
        }

    return render_template('admin/looker_usage.html', analysis_data=analysis_data)
