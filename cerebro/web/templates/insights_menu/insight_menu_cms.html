{% extends "layout.html" %}

{% block body %}
        <div id="form-errors" class="alert alert-danger alert-dismissible fade show mt-3 mb-3" style="display: none" role="alert">
            <span id="form-errors-span">

            </span>
        </div>
        <div id="form-success" class="alert alert-success alert-dismissible fade show mt-3 mb-3" style="display: none" role="alert">
                <span id="form-success-span">

                </span>
        </div>

        <form action="/insights_menu_cms" method="post">
            <div id="request_form">

            <input type="hidden" class="form-control" name="created_by" id="created_by" value="{{ current_user.name }}">

            <input type="hidden" class="form-control" name="is_edit" id="is_edit" value="no">

            <input type="hidden" class="form-control" name="report_id" id="report_id" value="">

            <div class="form-row">
                <div class="col">
                    <div class="form-group">
                        <label for="report_name">Report Title</label>
                        <input type="text" class="form-control" name="report_name" id="report_name" placeholder="">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="report_category">Category</label>
                        <select class="custom-select mr-sm-2" name="report_category" id="report_category">
                            <option value="">Select</option>
                            <option value="Executive Summary Service">Executive Summary Service</option>
                            <option value="Business Reporting Service">Business Reporting Service</option>
                            <option value="PPC Optimization Service">PPC Optimization Service</option>
                            <option value="New Market Guide Service">New Market Guide Service</option>
                            <option value="Brand Defence Service">Brand Defence Service</option>
                            <option value="Market Dashboard">Market Dashboard</option>
                            <option value="Brand Protection">Brand Protection</option>
                            <option value="Benchmarking">Benchmarking</option>
                            <option value="Prepare for a new launch">Prepare for a new launch</option>
                            <option value="Paid Search Optimisation">Paid Search Optimisation</option>
                            <option value="Market Change reaction">Market change reaction</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="col">
                    <div class="form-group">
                        <label for="insight_question">The question that the report is answering</label>
                        <textarea class="form-control" name="insight_question" id="insight_question" rows="2"></textarea>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="col">
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control" name="description" id="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="notes">Additional notes</label>
                        <textarea class="form-control" name="notes" id="notes" rows="3"></textarea>
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="col">
                    <div class="form-group">
                        <label for="report_link">YouTube link</label>
                        <input type="text" class="form-control" name="report_link" id="report_link" placeholder="">
                        <input type="hidden" class="form-control" name="report_file" id="report_file" placeholder="">
                    </div>
                </div>
                {#<div class="col">
                    <div class="form-group">
                        <label for="report_file">Report image / screenshot (not required if there is a youtube link)</label>

                    </div>
                </div>#}
            </div>
            <div class="form-row">
                <div class="col">
                    <div class="form-group">
                        <label for="lifecycle">Client lifecycle stage</label>
                        <select class="custom-select mr-sm-2" name="lifecycle" id="lifecycle">
                            <option value="">Select</option>
                            <option value="clients">Existing Clients</option>
                            <option value="renewal">Renewal Quarter</option>
                            <option value="bau">BAU</option>
                            <option value="qbr">QBR</option>
                            <option value="upsell">Upsell</option>
                            <option value="prospect">Prospect</option>
                            <option value="marketing">Marketing</option>
                            <option value="all">All stages</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="internal">Internal / External</label>
                        <select class="custom-select mr-sm-2" name="internal" id="internal">
                            <option value="">Select</option>
                            <option value="internal">Internal</option>
                            <option value="external">External</option>
                            <option value="all">All</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="industry">Industry</label>
                        <select class="custom-select mr-sm-2" name="industry" id="industry">
                            <option value="">Select</option>
                            <option value="Finance">Finance</option>
                            <option value="Retail">Retail</option>
                            <option value="Travel">Travel</option>
                            <option value="Automotive">Automotive</option>
                            <option value="Technology">Technology</option>
                            <option value="Gaming">Gaming</option>
                            <option value="Education">Education</option>
                            <option value="Energy & Utilities">Energy & Utilities</option>
                            <option value="Healthcare">Healthcare</option>
                            <option value="Legal and Professional Services">Legal and Professional Services</option>
                            <option value="Telecom">Telecom</option>
                            <option value="Leisure">Leisure</option>
                            <option value="Entertainment & Nightlife">Entertainment & Nightlife</option>
                            <option value="Property">Property</option>
                            <option value="all">All</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="col">
                    <div class="form-group">
                        <label for="persona">Target persona? (Buck, Bonnie, Clive?)</label>
                        <select class="custom-select mr-sm-2" name="persona" id="persona">
                            <option value="">Select</option>
                            <option value="buck">Buck</option>
                            <option value="bonnie">Bonnie</option>
                            <option value="clive">Clive</option>
                            <option value="all">All</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                    <label for="delivery">Report Delivery Speed</label>
                        <select class="custom-select mr-sm-2" name="delivery" id="delivery">
                            <option value="">Select</option>
                            <option value="fast">Fast</option>
                            <option value="standard">Standard</option>
                            <option value="bonnie">Bespoke</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="hours">Hours to build</label>
                        <input type="text" class="form-control" name="hours" id="hours" placeholder="">
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="col">
                    <div class="form-group">
                        <label for="enabled">Enabled?</label>
                        <select class="custom-select mr-sm-2" name="enabled" id="enabled">
                            <option value="disabled">Disabled</option>
                            <option value="enabled">Enabled</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="region">National or local view?</label>
                        <select class="custom-select mr-sm-2" name="region" id="region">
                            <option value="national">National</option>
                            <option value="local">Local</option>
                            <option value="all">All</option>
                        </select>
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="wholemarket">Data required</label>
                        <select class="custom-select" name="wholemarket" id="wholemarket">
                            <option value="">Select</option>
                            <option value="wholemarket">Whole Market</option>
                            <option value="myterms">My Terms</option>
                            <option value="all">All</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <label for="deliverables">Deliverables</label>
            </div>
            <div class="form-row">
                <div class="col">
                        <input type="checkbox" name="pdf" id="pdf" value="PDF">&nbsp;&nbsp;PDF
                </div>
                <div class="col">
                        <input type="checkbox" name="tableau" id="tableau" value="Tableau Workbook">&nbsp;&nbsp;Tableau Workbook
                </div>
                <div class="col">
                        <input type="checkbox" name="interactive_tableau" id="interactive_tableau" value="Interactive Tableau Dashboard">&nbsp;&nbsp;Interactive Tableau Dashboard
                </div>
            </div>
            <div class="form-row">
                <div class="col">
                        <input type="checkbox" name="spreadsheet" id="spreadsheet" value="Spreadsheet">&nbsp;&nbsp;Spreadsheet<br>
                </div>
                <div class="col">
                        <input type="checkbox" name="google_slides" id="google_slides" value="Google Slides">&nbsp;&nbsp;Google Slides<br>
                </div>
                <div class="col">
                </div>
            </div>
            <div class="form-row">
                <div class="col">
                    <div class="form-group">
                        <label for="deliverable">Other deliverable</label>
                        <input type="text" class="form-control" name="deliverable" id="deliverable" placeholder="">
                    </div>
                </div>
            </div>

            <div class="form-row" style="margin-top: 10px;margin-bottom: 50px;">
                <div class="col">
                    <input type="submit">
                </div>
                <br><br><br><br><br>
            </div>
        </div>
        </form>

        <div id="success_response" style="display: none;">
            <div class="card text-center">
                <div class="card-header">
                    Success
                </div>
                <div class="card-body">
                    <h5 class="card-title">Insight request created</h5>
                    <div class="card-text">
                        <p>Congratulations {{ current_user.name  }} your request has been created in Asana.</p>
                        <p>You will receive email updates on progress. Please see the FAQ for any questions on the process.</p>
                    </div>
                </div>
                <div class="card-footer text-muted">
                    Adthena Business Intelligence Team
                </div>
            </div>
        </div>

{% endblock %}

{% block scripts %}

    <script>
        $(document).ready(function() {
            let message = "{{ message | safe }}";
            let status = "{{ status | safe }}";
            let report_data = {{ report_data | safe }};

            console.log(message);
            console.log(status);

            if (report_data['report_name']) {
                console.log(report_data);
                console.log('edit');
                $('#is_edit').attr('value','yes');
                $('#report_id').val(report_data['report_id'][0]);
                $('#created_by').val(report_data['created_by'][0]);
                $('#report_name').val(report_data['report_name'][0]);
                $('#report_category').val(report_data['report_category'][0]);
                $('#insight_question').val(report_data['insight_question'][0]);
                $('#description').val(report_data['description'][0]);
                $('#notes').val(report_data['notes'][0]);
                $('#report_link').val(report_data['report_link'][0]);
                $('#report_file').val(report_data['report_file'][0]);
                $('#lifecycle').val(report_data['lifecycle'][0]);
                $('#internal').val(report_data['internal'][0]);
                $('#industry').val(report_data['industry'][0]);
                $('#persona').val(report_data['persona'][0]);
                $('#delivery').val(report_data['delivery'][0]);
                $('#hours').val(report_data['hours'][0]);
                $('#enabled').val(report_data['enabled'][0]);
                $('#region').val(report_data['region'][0]);
                $('#wholemarket').val(report_data['wholemarket'][0]);
                $('#deliverable').val(report_data['deliverable'][0]);

                if (report_data['pdf'][0] === 'yes') {
                    $('#pdf').prop('checked', true);
                }
                if (report_data['tableau'][0] === 'yes') {
                    $('#tableau').prop('checked', true);
                }
                if (report_data['interactive_tableau'][0] === 'yes') {
                    $('#interactive_tableau').prop('checked', true);
                }
                if (report_data['spreadsheet'][0] === 'yes') {
                    $('#spreadsheet').prop('checked', true);
                }
                if (report_data['google_slides'][0] === 'yes') {
                    $('#google_slides').prop('checked', true);
                }

                $('#deliverable').val(report_data['deliverable'][0]);
            }

            if (status === 'error') {
                $('#form-errors').show();
                $('#form-errors-span').text(message);
            } else {
                $('#form-errors').hide();
            }

            if (status === 'success') {
                $('#form-success').show();
                $('#form-success-span').text(message);
            } else {
                $('#form-success').hide();
            }
        });
    </script>


{% endblock %}
